/**
 * AnimatedSidebar - 帶有動畫的側邊欄組件
 * 提供流暢的展開/收縮動畫效果
 */

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useMotionPreferences } from './MotionProvider';

interface AnimatedSidebarProps {
  children: React.ReactNode;
  isOpen: boolean;
  width?: number;
  className?: string;
  style?: React.CSSProperties;
  position?: 'left' | 'right';
  overlay?: boolean;
  onOverlayClick?: () => void;
}

const AnimatedSidebar: React.FC<AnimatedSidebarProps> = ({
  children,
  isOpen,
  width = 400,
  className = '',
  style = {},
  position = 'left',
  overlay = false,
  onOverlayClick,
}) => {
  const { prefersReducedMotion, animationSpeed } = useMotionPreferences();

  // 側邊欄動畫變體
  const sidebarVariants = {
    open: {
      x: 0,
      opacity: 1,
      transition: {
        duration: prefersReducedMotion ? 0 : 0.3 / animationSpeed,
        ease: 'easeOut',
      },
    },
    closed: {
      x: position === 'left' ? -width : width,
      opacity: prefersReducedMotion ? 0 : 1,
      transition: {
        duration: prefersReducedMotion ? 0 : 0.3 / animationSpeed,
        ease: 'easeIn',
      },
    },
  };

  // 遮罩動畫變體
  const overlayVariants = {
    open: {
      opacity: 0.5,
      transition: {
        duration: prefersReducedMotion ? 0 : 0.2 / animationSpeed,
      },
    },
    closed: {
      opacity: 0,
      transition: {
        duration: prefersReducedMotion ? 0 : 0.2 / animationSpeed,
      },
    },
  };

  // 內容動畫變體
  const contentVariants = {
    open: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: prefersReducedMotion ? 0 : 0.2 / animationSpeed,
        delay: prefersReducedMotion ? 0 : 0.1 / animationSpeed,
      },
    },
    closed: {
      opacity: 0,
      scale: 0.95,
      transition: {
        duration: prefersReducedMotion ? 0 : 0.1 / animationSpeed,
      },
    },
  };

  return (
    <>
      {/* 遮罩層 (僅在 overlay 模式下顯示) */}
      <AnimatePresence>
        {overlay && isOpen && (
          <motion.div
            className="fixed inset-0 bg-black z-40"
            variants={overlayVariants}
            initial="closed"
            animate="open"
            exit="closed"
            onClick={onOverlayClick}
            style={{ pointerEvents: 'auto' }}
          />
        )}
      </AnimatePresence>

      {/* 側邊欄 */}
      <AnimatePresence>
        {(isOpen || !overlay) && (
          <motion.div
            className={`${overlay ? 'fixed' : 'relative'} top-0 ${
              position === 'left' ? 'left-0' : 'right-0'
            } h-full z-50 ${className}`}
            style={{
              width: overlay || isOpen ? width : 0,
              background: 'var(--color-surface-primary)',
              borderRight: position === 'left' ? '1px solid var(--color-border-primary)' : 'none',
              borderLeft: position === 'right' ? '1px solid var(--color-border-primary)' : 'none',
              boxShadow: 'var(--shadow-lg)',
              overflow: 'hidden',
              ...style,
            }}
            variants={overlay ? sidebarVariants : undefined}
            initial={overlay ? 'closed' : undefined}
            animate={overlay ? (isOpen ? 'open' : 'closed') : undefined}
            exit={overlay ? 'closed' : undefined}
            layout={!overlay}
          >
            {/* 側邊欄內容 */}
            <motion.div
              className="h-full w-full"
              variants={contentVariants}
              initial="closed"
              animate={isOpen ? 'open' : 'closed'}
            >
              {children}
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default AnimatedSidebar;
