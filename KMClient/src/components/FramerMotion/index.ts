/**
 * Framer Motion 動效組件庫索引
 * 導出所有現代化的動畫組件
 */

// 核心提供者
export { default as MotionProvider, useMotionPreferences } from './MotionProvider';

// 頁面過渡動畫
export { 
  default as PageTransition, 
  PageTransitionContainer,
  type TransitionType 
} from './PageTransition';

// 列表動畫
export { 
  default as ListAnimation, 
  ListItemAnimation,
  type ListAnimationType 
} from './ListAnimation';

// 交互動畫
export { 
  default as InteractiveMotion,
  HoverMotion,
  TapMotion,
  FocusMotion,
  ButtonMotion,
  CardMotion,
  type InteractionType 
} from './InteractiveMotion';

// 加載動畫
export {
  default as LoadingAnimation,
  Skeleton,
  TextSkeleton,
  type LoadingType
} from './LoadingAnimations';

// 路由動畫
export { default as AnimatedRoute } from './AnimatedRoute';

// 側邊欄動畫
export { default as AnimatedSidebar } from './AnimatedSidebar';

// 模態框動畫
export {
  default as AnimatedModal,
  type ModalAnimationType
} from './AnimatedModal';

// 按鈕動畫
export {
  default as AnimatedButton,
  ScaleButton,
  LiftButton,
  GlowButton,
  BounceButton
} from './AnimatedButton';

// 輸入框動畫
export {
  default as AnimatedInput,
  AnimatedTextArea,
  ScaleInput,
  GlowInput,
  LiftInput
} from './AnimatedInput';

// 狀態指示器
export {
  default as StatusIndicator,
  ProgressIndicator,
  PulseIndicator
} from './StatusIndicators';

// 動畫設置
export {
  default as AnimationSettings,
  SimpleAnimationSettings
} from './AnimationSettings';

// 性能監控
export { default as PerformanceMonitor } from './PerformanceMonitor';

// 類型定義
export type { default as MotionProviderProps } from './MotionProvider';
export type { default as PageTransitionProps } from './PageTransition';
export type { default as ListAnimationProps } from './ListAnimation';
export type { default as InteractiveMotionProps } from './InteractiveMotion';
export type { default as LoadingAnimationProps } from './LoadingAnimations';
