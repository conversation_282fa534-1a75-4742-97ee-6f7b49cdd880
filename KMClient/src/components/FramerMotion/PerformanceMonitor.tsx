/**
 * PerformanceMonitor - 動畫性能監控組件
 * 監控和顯示動畫性能指標（僅在開發環境中使用）
 */

import React, { useState, useEffect, useRef } from 'react';
import { Card, Typography, Space, Progress, Badge } from 'antd';
import { motion } from 'framer-motion';

const { Text } = Typography;

interface PerformanceMetrics {
  fps: number;
  frameTime: number;
  animationCount: number;
  memoryUsage?: number;
}

interface PerformanceMonitorProps {
  enabled?: boolean;
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  className?: string;
  style?: React.CSSProperties;
}

const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({
  enabled = process.env.NODE_ENV === 'development',
  position = 'bottom-right',
  className = '',
  style = {},
}) => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    fps: 60,
    frameTime: 16.67,
    animationCount: 0,
  });
  
  const [isVisible, setIsVisible] = useState(false);
  const frameCountRef = useRef(0);
  const lastTimeRef = useRef(performance.now());
  const animationFrameRef = useRef<number>();

  // 計算 FPS
  useEffect(() => {
    if (!enabled) return;

    const calculateFPS = () => {
      const now = performance.now();
      const delta = now - lastTimeRef.current;
      
      frameCountRef.current++;
      
      if (delta >= 1000) { // 每秒更新一次
        const fps = Math.round((frameCountRef.current * 1000) / delta);
        const frameTime = delta / frameCountRef.current;
        
        setMetrics(prev => ({
          ...prev,
          fps,
          frameTime: Math.round(frameTime * 100) / 100,
        }));
        
        frameCountRef.current = 0;
        lastTimeRef.current = now;
      }
      
      animationFrameRef.current = requestAnimationFrame(calculateFPS);
    };

    animationFrameRef.current = requestAnimationFrame(calculateFPS);

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [enabled]);

  // 監控內存使用（如果瀏覽器支持）
  useEffect(() => {
    if (!enabled) return;

    const updateMemoryUsage = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        setMetrics(prev => ({
          ...prev,
          memoryUsage: Math.round(memory.usedJSHeapSize / 1024 / 1024),
        }));
      }
    };

    const interval = setInterval(updateMemoryUsage, 2000);
    return () => clearInterval(interval);
  }, [enabled]);

  // 獲取性能等級
  const getPerformanceLevel = (fps: number) => {
    if (fps >= 55) return { level: 'excellent', color: '#52c41a', text: '優秀' };
    if (fps >= 45) return { level: 'good', color: '#1890ff', text: '良好' };
    if (fps >= 30) return { level: 'fair', color: '#faad14', text: '一般' };
    return { level: 'poor', color: '#ff4d4f', text: '較差' };
  };

  const performanceLevel = getPerformanceLevel(metrics.fps);

  // 位置樣式
  const getPositionStyle = () => {
    const baseStyle = {
      position: 'fixed' as const,
      zIndex: 9999,
      width: 200,
    };

    switch (position) {
      case 'top-left':
        return { ...baseStyle, top: 16, left: 16 };
      case 'top-right':
        return { ...baseStyle, top: 16, right: 16 };
      case 'bottom-left':
        return { ...baseStyle, bottom: 16, left: 16 };
      case 'bottom-right':
        return { ...baseStyle, bottom: 16, right: 16 };
      default:
        return { ...baseStyle, bottom: 16, right: 16 };
    }
  };

  if (!enabled) return null;

  return (
    <motion.div
      style={getPositionStyle()}
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: isVisible ? 1 : 0.7, scale: 1 }}
      whileHover={{ opacity: 1, scale: 1.05 }}
      onHoverStart={() => setIsVisible(true)}
      onHoverEnd={() => setIsVisible(false)}
    >
      <Card
        size="small"
        className={className}
        style={{
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          backdropFilter: 'blur(8px)',
          border: '1px solid rgba(255, 255, 255, 0.1)',
          ...style,
        }}
        bodyStyle={{ padding: 12 }}
      >
        <Space direction="vertical" size="small" style={{ width: '100%' }}>
          {/* 標題 */}
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Text strong style={{ color: 'white', fontSize: '12px' }}>
              性能監控
            </Text>
            <Badge
              color={performanceLevel.color}
              text={
                <Text style={{ color: 'white', fontSize: '10px' }}>
                  {performanceLevel.text}
                </Text>
              }
            />
          </div>

          {/* FPS */}
          <div>
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4 }}>
              <Text style={{ color: 'rgba(255, 255, 255, 0.8)', fontSize: '11px' }}>
                FPS
              </Text>
              <Text style={{ color: 'white', fontSize: '11px' }}>
                {metrics.fps}
              </Text>
            </div>
            <Progress
              percent={(metrics.fps / 60) * 100}
              size="small"
              strokeColor={performanceLevel.color}
              showInfo={false}
            />
          </div>

          {/* 幀時間 */}
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <Text style={{ color: 'rgba(255, 255, 255, 0.8)', fontSize: '11px' }}>
              幀時間
            </Text>
            <Text style={{ color: 'white', fontSize: '11px' }}>
              {metrics.frameTime}ms
            </Text>
          </div>

          {/* 內存使用 */}
          {metrics.memoryUsage && (
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Text style={{ color: 'rgba(255, 255, 255, 0.8)', fontSize: '11px' }}>
                內存
              </Text>
              <Text style={{ color: 'white', fontSize: '11px' }}>
                {metrics.memoryUsage}MB
              </Text>
            </div>
          )}

          {/* 提示 */}
          {isVisible && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              style={{
                borderTop: '1px solid rgba(255, 255, 255, 0.1)',
                paddingTop: 8,
                marginTop: 4,
              }}
            >
              <Text style={{ color: 'rgba(255, 255, 255, 0.6)', fontSize: '10px' }}>
                {metrics.fps < 30 && '建議開啟"減少動畫"以提升性能'}
                {metrics.fps >= 30 && metrics.fps < 45 && '性能一般，可考慮調整動畫設置'}
                {metrics.fps >= 45 && '性能良好，動畫運行流暢'}
              </Text>
            </motion.div>
          )}
        </Space>
      </Card>
    </motion.div>
  );
};

export default PerformanceMonitor;
