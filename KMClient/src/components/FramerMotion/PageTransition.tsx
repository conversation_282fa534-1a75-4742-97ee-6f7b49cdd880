/**
 * PageTransition - 頁面切換動畫組件
 * 提供多種頁面過渡效果
 */

import React from 'react';
import { motion, AnimatePresence, Variants } from 'framer-motion';
import { useMotionPreferences } from './MotionProvider';

export type TransitionType = 
  | 'fade' 
  | 'slide' 
  | 'scale' 
  | 'slideUp' 
  | 'slideDown' 
  | 'slideLeft' 
  | 'slideRight'
  | 'scaleRotate'
  | 'blur';

interface PageTransitionProps {
  children: React.ReactNode;
  type?: TransitionType;
  duration?: number;
  className?: string;
  style?: React.CSSProperties;
}

const PageTransition: React.FC<PageTransitionProps> = ({
  children,
  type = 'fade',
  duration = 0.3,
  className = '',
  style = {},
}) => {
  const { prefersReducedMotion, animationSpeed } = useMotionPreferences();

  // 動畫變體定義
  const getVariants = (): Variants => {
    const adjustedDuration = prefersReducedMotion ? 0 : duration / animationSpeed;

    switch (type) {
      case 'fade':
        return {
          initial: { opacity: 0 },
          animate: { opacity: 1 },
          exit: { opacity: 0 },
        };

      case 'slide':
      case 'slideRight':
        return {
          initial: { opacity: 0, x: 100 },
          animate: { opacity: 1, x: 0 },
          exit: { opacity: 0, x: -100 },
        };

      case 'slideLeft':
        return {
          initial: { opacity: 0, x: -100 },
          animate: { opacity: 1, x: 0 },
          exit: { opacity: 0, x: 100 },
        };

      case 'slideUp':
        return {
          initial: { opacity: 0, y: 100 },
          animate: { opacity: 1, y: 0 },
          exit: { opacity: 0, y: -100 },
        };

      case 'slideDown':
        return {
          initial: { opacity: 0, y: -100 },
          animate: { opacity: 1, y: 0 },
          exit: { opacity: 0, y: 100 },
        };

      case 'scale':
        return {
          initial: { opacity: 0, scale: 0.8 },
          animate: { opacity: 1, scale: 1 },
          exit: { opacity: 0, scale: 1.2 },
        };

      case 'scaleRotate':
        return {
          initial: { opacity: 0, scale: 0.8, rotate: -10 },
          animate: { opacity: 1, scale: 1, rotate: 0 },
          exit: { opacity: 0, scale: 0.8, rotate: 10 },
        };

      case 'blur':
        return {
          initial: { opacity: 0, filter: 'blur(10px)' },
          animate: { opacity: 1, filter: 'blur(0px)' },
          exit: { opacity: 0, filter: 'blur(10px)' },
        };

      default:
        return {
          initial: { opacity: 0 },
          animate: { opacity: 1 },
          exit: { opacity: 0 },
        };
    }
  };

  const variants = getVariants();

  return (
    <motion.div
      className={className}
      style={style}
      variants={variants}
      initial="initial"
      animate="animate"
      exit="exit"
      transition={{
        duration: prefersReducedMotion ? 0 : duration / animationSpeed,
        ease: 'easeOut',
      }}
    >
      {children}
    </motion.div>
  );
};

// 頁面切換容器組件
interface PageTransitionContainerProps {
  children: React.ReactNode;
  pageKey: string;
  type?: TransitionType;
  duration?: number;
  className?: string;
  style?: React.CSSProperties;
}

export const PageTransitionContainer: React.FC<PageTransitionContainerProps> = ({
  children,
  pageKey,
  type = 'fade',
  duration = 0.3,
  className = '',
  style = {},
}) => {
  return (
    <AnimatePresence mode="wait">
      <PageTransition
        key={pageKey}
        type={type}
        duration={duration}
        className={className}
        style={style}
      >
        {children}
      </PageTransition>
    </AnimatePresence>
  );
};

export default PageTransition;
