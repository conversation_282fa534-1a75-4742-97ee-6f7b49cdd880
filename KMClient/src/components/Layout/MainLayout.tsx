/**
 * 主要佈局組件 - 左右分欄設計
 */

import React, { useEffect } from 'react';
import { Layout, Button, Typography, Space, Tooltip } from 'antd';
import { MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons';
import { MdLightMode, MdDarkMode } from 'react-icons/md';
import { useAppStore } from '@/hooks/useAppStore';
import AttachmentPanel from '@/components/AttachmentPanel/AttachmentPanel';
import ChatPanel from '@/components/ChatPanel/ChatPanel';
import { AnimatedBackground, AnimatedText } from '@/components/ReactBits';
import { PerformanceMonitor, SimpleAnimationSettings } from '@/components/FramerMotion';

const { Header, Sider, Content } = Layout;
const { Title } = Typography;

interface MainLayoutProps {
  children?: React.ReactNode;
}

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const { sidebarCollapsed, toggleSidebar, isDarkMode, toggleDarkMode } = useAppStore();

  return (
    <Layout className="min-h-screen">
      {/* 左側附件管理面板 */}
      <Sider
        trigger={null}
        collapsible
        collapsed={sidebarCollapsed}
        width={400}
        collapsedWidth={0}
        className="transition-all duration-300"
        style={{
          background: 'var(--color-surface-primary)',
          borderRight: '1px solid var(--color-border-primary)',
          boxShadow: 'var(--shadow-lg)',
          transition: 'all 0.3s ease'
        }}
        breakpoint="lg"
        onBreakpoint={(broken) => {
          // 在小屏幕上自動收起側邊欄
          if (broken && !sidebarCollapsed) {
            toggleSidebar();
          }
        }}
      >
        <div className="h-full flex flex-col">
          {/* 附件面板標題 */}
          <div
            className="p-4 border-b transition-colors duration-300"
            style={{
              borderBottom: '1px solid var(--color-border-primary)',
              background: 'var(--color-surface-secondary)'
            }}
          >
            <Title level={4} className="!mb-0" style={{ color: 'var(--color-neon-blue)' }}>
              📎 附件管理
            </Title>
            <div className="text-xs mt-1" style={{ color: 'var(--color-text-tertiary)' }}>
              管理您的檔案、網站和文本內容
            </div>
          </div>
          
          {/* 附件管理組件 */}
          <div className="flex-1 overflow-hidden">
            <AttachmentPanel />
          </div>
        </div>
      </Sider>

      {/* 主內容區域 */}
      <Layout>
        {/* 頂部導航欄 */}
        <Header
          className="px-4 flex items-center justify-between transition-colors duration-300"
          style={{
            background: 'var(--color-surface-primary)',
            borderBottom: '1px solid var(--color-border-primary)',
            height: '64px'
          }}
        >
          <Space align="center">
            {/* 側邊欄切換按鈕 */}
            <Button
              type="text"
              icon={sidebarCollapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={toggleSidebar}
              className="hover:bg-opacity-10 hover:bg-gray-500 transition-colors duration-200"
              style={{
                color: 'var(--color-text-secondary)',
                fontSize: '16px'
              }}
            />

            {/* 應用標題 */}
            <AnimatedText
              text="🧠 KM Client"
              animation="glow"
              duration={1}
              as="h3"
              className="!mb-0 text-xl font-bold"
              style={{ color: 'var(--color-neon-blue)' }}
            />
          </Space>

          {/* 右側操作按鈕 */}
          <Space>
            <SimpleAnimationSettings />
            <Tooltip title={isDarkMode ? '切換到淺色模式' : '切換到深色模式'}>
              <Button
                type="text"
                icon={isDarkMode ? <MdLightMode /> : <MdDarkMode />}
                onClick={toggleDarkMode}
                className="hover:bg-opacity-10 hover:bg-gray-500 transition-colors duration-200"
                style={{ color: 'var(--color-text-secondary)' }}
              />
            </Tooltip>
          </Space>
        </Header>

        {/* 主內容區域 - 聊天面板 */}
        <Content
          className="transition-colors duration-300 relative"
          style={{
            background: 'var(--color-bg-secondary)',
            minHeight: 'calc(100vh - 64px)'
          }}
        >
          <AnimatedBackground
            type="particles"
            opacity={0.05}
            speed={0.5}
            density={30}
          >
            {children || <ChatPanel />}
          </AnimatedBackground>
        </Content>
      </Layout>

      {/* 性能監控器（僅在開發環境中顯示） */}
      <PerformanceMonitor />
    </Layout>
  );
};

export default MainLayout;
