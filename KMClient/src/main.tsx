import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import { MotionProvider } from '@/components/FramerMotion';
import './styles/index.css';

// 初始化主題設置
const initializeTheme = () => {
  try {
    // 從 localStorage 讀取主題設置
    const savedTheme = localStorage.getItem('kmClient_darkMode');
    const isDarkMode = savedTheme === 'true';

    // 立即應用主題類名，避免閃爍
    if (isDarkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }

    console.log('🎨 主題初始化完成:', isDarkMode ? '深色模式' : '淺色模式');
  } catch (error) {
    console.error('主題初始化失敗:', error);
    // 默認使用深色模式
    document.documentElement.classList.add('dark');
  }
};

// 隱藏載入畫面
const hideLoading = () => {
  const loading = document.getElementById('loading');
  if (loading) {
    loading.style.display = 'none';
  }
};

// 在渲染應用前初始化主題
initializeTheme();

// 渲染應用程式
ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <MotionProvider>
      <App />
    </MotionProvider>
  </React.StrictMode>
);

// 應用載入完成後隱藏載入畫面
setTimeout(hideLoading, 100);
